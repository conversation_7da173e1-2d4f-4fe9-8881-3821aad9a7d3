{"cells": [{"cell_type": "code", "execution_count": 2, "id": "276300db", "metadata": {}, "outputs": [], "source": ["data = [\n", "    {\n", "        \"id\": 1834948,\n", "        \"created_at\": \"2025-06-11T10:46:38.491310+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:38.491319+08:00\",\n", "        \"transaction_id\": \"284178\",\n", "        \"line_id\": \"4\",\n", "        \"transaction_date\": \"2024-01-01\",\n", "        \"memo\": \"A/P Invoices - CAPER-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"0.04\",\n", "        \"account_code\": \"_SYS00000000828\",\n", "        \"offset_account_code\": \"CAPER-NOV\",\n", "        \"reference_1\": \"3008815\",\n", "        \"reference_2\": \"#9903\",\n", "        \"base_reference\": \"3008815\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 449992,\n", "        \"chart_of_account\": 12513,\n", "    },\n", "    {\n", "        \"id\": 1835295,\n", "        \"created_at\": \"2025-06-11T10:46:39.699121+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.699130+08:00\",\n", "        \"transaction_id\": \"284298\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Credit Memos - TECBTC-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"84.00\",\n", "        \"reporting_credit_amount\": \"0.00\",\n", "        \"account_code\": \"_SYS00000000825\",\n", "        \"offset_account_code\": \"TECBTC-NOV\",\n", "        \"reference_1\": \"3000924\",\n", "        \"reference_2\": \"\",\n", "        \"base_reference\": \"3000924\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450102,\n", "        \"chart_of_account\": 12510,\n", "    },\n", "    {\n", "        \"id\": 1835300,\n", "        \"created_at\": \"2025-06-11T10:46:39.715774+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.715784+08:00\",\n", "        \"transaction_id\": \"284300\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - BCVICG-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"636.00\",\n", "        \"account_code\": \"_SYS00000000823\",\n", "        \"offset_account_code\": \"BCVICG-N-NOV\",\n", "        \"reference_1\": \"3048899\",\n", "        \"reference_2\": \"IN202401.BCVICG-N-NOV\",\n", "        \"base_reference\": \"3048899\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450104,\n", "        \"chart_of_account\": 12508,\n", "    },\n", "    {\n", "        \"id\": 1835307,\n", "        \"created_at\": \"2025-06-11T10:46:39.740834+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.740844+08:00\",\n", "        \"transaction_id\": \"284303\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-11\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"120.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048900\",\n", "        \"reference_2\": \"INNMC-159921\",\n", "        \"base_reference\": \"3048900\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450107,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835312,\n", "        \"created_at\": \"2025-06-11T10:46:39.757290+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.757300+08:00\",\n", "        \"transaction_id\": \"284305\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"540.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048901\",\n", "        \"reference_2\": \"INNMC-159080\",\n", "        \"base_reference\": \"3048901\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450109,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835315,\n", "        \"created_at\": \"2025-06-11T10:46:39.767040+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.767049+08:00\",\n", "        \"transaction_id\": \"284306\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"950.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048902\",\n", "        \"reference_2\": \"INNMC-159081\",\n", "        \"base_reference\": \"3048902\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450110,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835318,\n", "        \"created_at\": \"2025-06-11T10:46:39.776567+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.776576+08:00\",\n", "        \"transaction_id\": \"284307\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"280.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048903\",\n", "        \"reference_2\": \"INNMC-159082\",\n", "        \"base_reference\": \"3048903\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450111,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835322,\n", "        \"created_at\": \"2025-06-11T10:46:39.788341+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.788350+08:00\",\n", "        \"transaction_id\": \"284308\",\n", "        \"line_id\": \"3\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - SGTMS-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"4312.80\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"SGTMS-N-NOV\",\n", "        \"reference_1\": \"3048904\",\n", "        \"reference_2\": \"IN202401.SGTMS-N-NOV\",\n", "        \"base_reference\": \"3048904\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450112,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835323,\n", "        \"created_at\": \"2025-06-11T10:46:39.790726+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.790735+08:00\",\n", "        \"transaction_id\": \"284308\",\n", "        \"line_id\": \"4\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - SGTMS-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"0.02\",\n", "        \"account_code\": \"_SYS00000000828\",\n", "        \"offset_account_code\": \"SGTMS-N-NOV\",\n", "        \"reference_1\": \"3048904\",\n", "        \"reference_2\": \"IN202401.SGTMS-N-NOV\",\n", "        \"base_reference\": \"3048904\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450112,\n", "        \"chart_of_account\": 12513,\n", "    },\n", "    {\n", "        \"id\": 1835326,\n", "        \"created_at\": \"2025-06-11T10:46:39.800232+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.800242+08:00\",\n", "        \"transaction_id\": \"284309\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - OSTCM-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"533.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"OSTCM-NOV\",\n", "        \"reference_1\": \"3048905\",\n", "        \"reference_2\": \"IN202401.OSTCM-NOV\",\n", "        \"base_reference\": \"3048905\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450113,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835329,\n", "        \"created_at\": \"2025-06-11T10:46:39.809510+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.809521+08:00\",\n", "        \"transaction_id\": \"284310\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"20.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048906\",\n", "        \"reference_2\": \"INNMC-159085\",\n", "        \"base_reference\": \"3048906\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450114,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835332,\n", "        \"created_at\": \"2025-06-11T10:46:39.819019+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.819030+08:00\",\n", "        \"transaction_id\": \"284311\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"650.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048907\",\n", "        \"reference_2\": \"INNMC-159086\",\n", "        \"base_reference\": \"3048907\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450115,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835335,\n", "        \"created_at\": \"2025-06-11T10:46:39.828684+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.828695+08:00\",\n", "        \"transaction_id\": \"284312\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"280.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048908\",\n", "        \"reference_2\": \"INNMC-159087\",\n", "        \"base_reference\": \"3048908\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450116,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835338,\n", "        \"created_at\": \"2025-06-11T10:46:39.838701+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.838713+08:00\",\n", "        \"transaction_id\": \"284313\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - TTLTMS-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"4187.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"TTLTMS-N-NOV\",\n", "        \"reference_1\": \"3048909\",\n", "        \"reference_2\": \"IN202401.TTLTMS-N-NOV\",\n", "        \"base_reference\": \"3048909\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450117,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835341,\n", "        \"created_at\": \"2025-06-11T10:46:39.849105+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.849117+08:00\",\n", "        \"transaction_id\": \"284314\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - SCSAOC-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"21021.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"SCSAOC-N-NOV\",\n", "        \"reference_1\": \"3048910\",\n", "        \"reference_2\": \"IN202401.SCSAOC-N-NOV\",\n", "        \"base_reference\": \"3048910\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450118,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835344,\n", "        \"created_at\": \"2025-06-11T10:46:39.859150+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.859162+08:00\",\n", "        \"transaction_id\": \"284315\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - ERNGC-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"20321.85\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"ERNGC-N-NOV\",\n", "        \"reference_1\": \"3048911\",\n", "        \"reference_2\": \"IN202401.ERNGC-N-NOV\",\n", "        \"base_reference\": \"3048911\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450119,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835348,\n", "        \"created_at\": \"2025-06-11T10:46:39.871352+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.871363+08:00\",\n", "        \"transaction_id\": \"284316\",\n", "        \"line_id\": \"3\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - NPC-NOV-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"15820.05\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"NPC-NOV-NOV\",\n", "        \"reference_1\": \"3048912\",\n", "        \"reference_2\": \"IN202401.NPC-NOV-NOV\",\n", "        \"base_reference\": \"3048912\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450120,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835349,\n", "        \"created_at\": \"2025-06-11T10:46:39.873664+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.873673+08:00\",\n", "        \"transaction_id\": \"284316\",\n", "        \"line_id\": \"4\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - NPC-NOV-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"0.03\",\n", "        \"account_code\": \"_SYS00000000828\",\n", "        \"offset_account_code\": \"NPC-NOV-NOV\",\n", "        \"reference_1\": \"3048912\",\n", "        \"reference_2\": \"IN202401.NPC-NOV-NOV\",\n", "        \"base_reference\": \"3048912\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450120,\n", "        \"chart_of_account\": 12513,\n", "    },\n", "    {\n", "        \"id\": 1835352,\n", "        \"created_at\": \"2025-06-11T10:46:39.883622+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.883632+08:00\",\n", "        \"transaction_id\": \"284317\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - LWCC-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"5209.60\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"LWCC-NOV\",\n", "        \"reference_1\": \"3048913\",\n", "        \"reference_2\": \"IN202401.LWCC-NOV\",\n", "        \"base_reference\": \"3048913\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450121,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835355,\n", "        \"created_at\": \"2025-06-11T10:46:39.893167+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.893178+08:00\",\n", "        \"transaction_id\": \"284318\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - NSAMC-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"768.50\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"NSAMC-NOV\",\n", "        \"reference_1\": \"3048914\",\n", "        \"reference_2\": \"IN202401.NSAMC-NOV\",\n", "        \"base_reference\": \"3048914\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450122,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835356,\n", "        \"created_at\": \"2025-06-11T10:46:39.895568+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.895577+08:00\",\n", "        \"transaction_id\": \"284318\",\n", "        \"line_id\": \"3\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - NSAMC-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"100.00\",\n", "        \"account_code\": \"_SYS00000000827\",\n", "        \"offset_account_code\": \"NSAMC-NOV\",\n", "        \"reference_1\": \"3048914\",\n", "        \"reference_2\": \"IN202401.NSAMC-NOV\",\n", "        \"base_reference\": \"3048914\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450122,\n", "        \"chart_of_account\": 12512,\n", "    },\n", "    {\n", "        \"id\": 1835359,\n", "        \"created_at\": \"2025-06-11T10:46:39.904837+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.904848+08:00\",\n", "        \"transaction_id\": \"284319\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - OPTM-SINGH-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"25690.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"OPTM-SINGH-NOV\",\n", "        \"reference_1\": \"3048915\",\n", "        \"reference_2\": \"IN202401.OPTM-SINGH-NOV\",\n", "        \"base_reference\": \"3048915\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450123,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835362,\n", "        \"created_at\": \"2025-06-11T10:46:39.913753+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.913765+08:00\",\n", "        \"transaction_id\": \"284320\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - CSTCM-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"3869.75\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"CSTCM-NOV\",\n", "        \"reference_1\": \"3048916\",\n", "        \"reference_2\": \"IN202401.CSTCM-NOV\",\n", "        \"base_reference\": \"3048916\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450124,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835365,\n", "        \"created_at\": \"2025-06-11T10:46:39.923196+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.923206+08:00\",\n", "        \"transaction_id\": \"284321\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - JJDBP-HF-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"190.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"JJDBP-HF-NOV\",\n", "        \"reference_1\": \"3048917\",\n", "        \"reference_2\": \"IN202401.JJDBP-HF-NOV\",\n", "        \"base_reference\": \"3048917\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450125,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835368,\n", "        \"created_at\": \"2025-06-11T10:46:39.933174+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.933185+08:00\",\n", "        \"transaction_id\": \"284322\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - TWSTMS-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"8684.40\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"TWSTMS-N-NOV\",\n", "        \"reference_1\": \"3048918\",\n", "        \"reference_2\": \"IN202401.TWSTMS-N-NOV\",\n", "        \"base_reference\": \"3048918\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450126,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835369,\n", "        \"created_at\": \"2025-06-11T10:46:39.935571+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.935581+08:00\",\n", "        \"transaction_id\": \"284322\",\n", "        \"line_id\": \"3\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - TWSTMS-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"300.00\",\n", "        \"account_code\": \"_SYS00000000827\",\n", "        \"offset_account_code\": \"TWSTMS-N-NOV\",\n", "        \"reference_1\": \"3048918\",\n", "        \"reference_2\": \"IN202401.TWSTMS-N-NOV\",\n", "        \"base_reference\": \"3048918\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450126,\n", "        \"chart_of_account\": 12512,\n", "    },\n", "    {\n", "        \"id\": 1835372,\n", "        \"created_at\": \"2025-06-11T10:46:39.945334+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.945344+08:00\",\n", "        \"transaction_id\": \"284323\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - MTCCMTS-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"14453.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"MTCCMTS-N-NOV\",\n", "        \"reference_1\": \"3048919\",\n", "        \"reference_2\": \"IN202401.MTCCMTS-N-NOV\",\n", "        \"base_reference\": \"3048919\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450127,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835373,\n", "        \"created_at\": \"2025-06-11T10:46:39.949304+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.949314+08:00\",\n", "        \"transaction_id\": \"284323\",\n", "        \"line_id\": \"3\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - MTCCMTS-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"850.00\",\n", "        \"account_code\": \"_SYS00000000823\",\n", "        \"offset_account_code\": \"MTCCMTS-N-NOV\",\n", "        \"reference_1\": \"3048919\",\n", "        \"reference_2\": \"IN202401.MTCCMTS-N-NOV\",\n", "        \"base_reference\": \"3048919\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450127,\n", "        \"chart_of_account\": 12508,\n", "    },\n", "    {\n", "        \"id\": 1835374,\n", "        \"created_at\": \"2025-06-11T10:46:39.951826+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.951837+08:00\",\n", "        \"transaction_id\": \"284323\",\n", "        \"line_id\": \"4\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - MTCCMTS-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"450.00\",\n", "        \"account_code\": \"_SYS00000000822\",\n", "        \"offset_account_code\": \"MTCCMTS-N-NOV\",\n", "        \"reference_1\": \"3048919\",\n", "        \"reference_2\": \"IN202401.MTCCMTS-N-NOV\",\n", "        \"base_reference\": \"3048919\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450127,\n", "        \"chart_of_account\": 12507,\n", "    },\n", "    {\n", "        \"id\": 1835375,\n", "        \"created_at\": \"2025-06-11T10:46:39.954108+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.954120+08:00\",\n", "        \"transaction_id\": \"284323\",\n", "        \"line_id\": \"5\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - MTCCMTS-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"200.00\",\n", "        \"account_code\": \"_SYS00000000819\",\n", "        \"offset_account_code\": \"MTCCMTS-N-NOV\",\n", "        \"reference_1\": \"3048919\",\n", "        \"reference_2\": \"IN202401.MTCCMTS-N-NOV\",\n", "        \"base_reference\": \"3048919\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450127,\n", "        \"chart_of_account\": 12504,\n", "    },\n", "    {\n", "        \"id\": 1835378,\n", "        \"created_at\": \"2025-06-11T10:46:39.965366+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.965377+08:00\",\n", "        \"transaction_id\": \"284324\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"1340.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048920\",\n", "        \"reference_2\": \"INNMC-159105\",\n", "        \"base_reference\": \"3048920\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450128,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835381,\n", "        \"created_at\": \"2025-06-11T10:46:39.975032+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.975042+08:00\",\n", "        \"transaction_id\": \"284325\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - ANCKJHC-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"10371.80\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"ANCKJHC-N-NOV\",\n", "        \"reference_1\": \"3048921\",\n", "        \"reference_2\": \"IN202401.ANCKJHC-N-NOV\",\n", "        \"base_reference\": \"3048921\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450129,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835384,\n", "        \"created_at\": \"2025-06-11T10:46:39.984490+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.984501+08:00\",\n", "        \"transaction_id\": \"284326\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - EHS-NOV-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"769.50\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"EHS-NOV-NOV\",\n", "        \"reference_1\": \"3048922\",\n", "        \"reference_2\": \"IN202401.EHS-NOV-NOV\",\n", "        \"base_reference\": \"3048922\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450130,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835387,\n", "        \"created_at\": \"2025-06-11T10:46:39.994154+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:39.994163+08:00\",\n", "        \"transaction_id\": \"284327\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"900.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048923\",\n", "        \"reference_2\": \"INNMC-159110\",\n", "        \"base_reference\": \"3048923\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450131,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835390,\n", "        \"created_at\": \"2025-06-11T10:46:40.015408+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.015425+08:00\",\n", "        \"transaction_id\": \"284328\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"15.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048924\",\n", "        \"reference_2\": \"INNMC-159111\",\n", "        \"base_reference\": \"3048924\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450132,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835393,\n", "        \"created_at\": \"2025-06-11T10:46:40.038289+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.038304+08:00\",\n", "        \"transaction_id\": \"284329\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - NCSPINTLC-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"4804.80\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"NCSPINTLC-NOV\",\n", "        \"reference_1\": \"3048925\",\n", "        \"reference_2\": \"IN202401.NCSPINTLC-NOV\",\n", "        \"base_reference\": \"3048925\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450133,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835396,\n", "        \"created_at\": \"2025-06-11T10:46:40.050787+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.050801+08:00\",\n", "        \"transaction_id\": \"284330\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"280.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048926\",\n", "        \"reference_2\": \"INNMC-159116\",\n", "        \"base_reference\": \"3048926\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450134,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835399,\n", "        \"created_at\": \"2025-06-11T10:46:40.062662+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.062676+08:00\",\n", "        \"transaction_id\": \"284331\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - AIA-SIN-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"5586.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"AIA-SIN-NOV\",\n", "        \"reference_1\": \"3048927\",\n", "        \"reference_2\": \"IN202401.AIA-SIN-NOV\",\n", "        \"base_reference\": \"3048927\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450135,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835402,\n", "        \"created_at\": \"2025-06-11T10:46:40.081276+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.081292+08:00\",\n", "        \"transaction_id\": \"284332\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"125.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048928\",\n", "        \"reference_2\": \"INNMC-159118\",\n", "        \"base_reference\": \"3048928\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450136,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835405,\n", "        \"created_at\": \"2025-06-11T10:46:40.091963+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.091978+08:00\",\n", "        \"transaction_id\": \"284333\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - AGESUP-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"13399.50\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"AGESUP-NOV\",\n", "        \"reference_1\": \"3048929\",\n", "        \"reference_2\": \"IN202401.AGESUP-NOV\",\n", "        \"base_reference\": \"3048929\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450137,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835408,\n", "        \"created_at\": \"2025-06-11T10:46:40.105882+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.105898+08:00\",\n", "        \"transaction_id\": \"284334\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"130.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048930\",\n", "        \"reference_2\": \"INNMC-159120\",\n", "        \"base_reference\": \"3048930\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450138,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835411,\n", "        \"created_at\": \"2025-06-11T10:46:40.117473+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.117488+08:00\",\n", "        \"transaction_id\": \"284335\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - ATMARINE-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"130.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"ATMARINE-NOV\",\n", "        \"reference_1\": \"3048931\",\n", "        \"reference_2\": \"IN202401.ATMARINE-NOV\",\n", "        \"base_reference\": \"3048931\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450139,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835414,\n", "        \"created_at\": \"2025-06-11T10:46:40.127839+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.127854+08:00\",\n", "        \"transaction_id\": \"284336\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - RSSRSN-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"7961.60\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"RSSRSN-N-NOV\",\n", "        \"reference_1\": \"3048932\",\n", "        \"reference_2\": \"IN202401.RSSRSN-N-NOV\",\n", "        \"base_reference\": \"3048932\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450140,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835417,\n", "        \"created_at\": \"2025-06-11T10:46:40.138676+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.138690+08:00\",\n", "        \"transaction_id\": \"284337\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - GLDTAP-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"91.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"GLDTAP-N-NOV\",\n", "        \"reference_1\": \"3048933\",\n", "        \"reference_2\": \"IN202401.GLDTAP-N-NOV\",\n", "        \"base_reference\": \"3048933\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450141,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835420,\n", "        \"created_at\": \"2025-06-11T10:46:40.149412+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.149425+08:00\",\n", "        \"transaction_id\": \"284338\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - CTHCFOHK-MN-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"18860.85\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"CTHCFOHK-MN-NOV\",\n", "        \"reference_1\": \"3048934\",\n", "        \"reference_2\": \"IN202401.CTHCFOHK-MN-NOV\",\n", "        \"base_reference\": \"3048934\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450142,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835423,\n", "        \"created_at\": \"2025-06-11T10:46:40.162097+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.162110+08:00\",\n", "        \"transaction_id\": \"284339\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"950.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048935\",\n", "        \"reference_2\": \"INNMC-159128\",\n", "        \"base_reference\": \"3048935\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450143,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835426,\n", "        \"created_at\": \"2025-06-11T10:46:40.171751+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.171764+08:00\",\n", "        \"transaction_id\": \"284340\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - HHCCFW-N-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"1495.75\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"HHCCFW-N-NOV\",\n", "        \"reference_1\": \"3048936\",\n", "        \"reference_2\": \"IN202401.HHCCFW-N-NOV\",\n", "        \"base_reference\": \"3048936\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450144,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835429,\n", "        \"created_at\": \"2025-06-11T10:46:40.182106+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.182119+08:00\",\n", "        \"transaction_id\": \"284341\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"760.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048937\",\n", "        \"reference_2\": \"INNMC-159131\",\n", "        \"base_reference\": \"3048937\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450145,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835432,\n", "        \"created_at\": \"2025-06-11T10:46:40.191829+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.191839+08:00\",\n", "        \"transaction_id\": \"284342\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-02\",\n", "        \"memo\": \"A/R Invoices - PATIENT-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"20.00\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"PATIENT-NOV\",\n", "        \"reference_1\": \"3048938\",\n", "        \"reference_2\": \"INNMC-159132\",\n", "        \"base_reference\": \"3048938\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450146,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "    {\n", "        \"id\": 1835435,\n", "        \"created_at\": \"2025-06-11T10:46:40.201355+08:00\",\n", "        \"updated_at\": \"2025-06-11T10:46:40.201365+08:00\",\n", "        \"transaction_id\": \"284343\",\n", "        \"line_id\": \"2\",\n", "        \"transaction_date\": \"2024-01-31\",\n", "        \"memo\": \"A/R Invoices - JC-HMC-NOV\",\n", "        \"transaction_currency\": \"SGD\",\n", "        \"transaction_debit_amount\": \"0.00\",\n", "        \"transaction_credit_amount\": \"0.00\",\n", "        \"reporting_currency\": \"SGD\",\n", "        \"reporting_debit_amount\": \"0.00\",\n", "        \"reporting_credit_amount\": \"591.80\",\n", "        \"account_code\": \"_SYS00000000821\",\n", "        \"offset_account_code\": \"JC-HMC-NOV\",\n", "        \"reference_1\": \"3048939\",\n", "        \"reference_2\": \"IN202401.JC-HMC-NOV\",\n", "        \"base_reference\": \"3048939\",\n", "        \"is_adjustment\": <PERSON><PERSON><PERSON>,\n", "        \"entity\": 6,\n", "        \"journal_entry\": 450147,\n", "        \"chart_of_account\": 12506,\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": 8, "id": "ff2bef4e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.DataFrame(data)\n", "\n", "df.head().to_csv(\"test.csv\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}