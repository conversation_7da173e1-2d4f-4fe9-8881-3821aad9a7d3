from accounting.models import ChartofAccount, JournalEntryTransaction


def get_all_child_coas(parent_coas):
    all_coas = list(parent_coas)  # Start with the parent COAs

    # Get immediate children
    children = ChartofAccount.objects.filter(parent__in=parent_coas)

    if children.exists():
        # Add children to the list
        all_coas.extend(list(children))
        # Recursively get children of children
        all_coas.extend(get_all_child_coas(children))

    return all_coas


def get_all_raw_data_snapshot(account_name, level, date=None):
    all_coas = ChartofAccount.objects.filter(
        account_name__icontains=account_name, level=level
    )
    all_leaf_coa = get_all_child_coas(all_coas)
    journal_entries = JournalEntryTransaction.objects.filter(
        chart_of_account__in=all_leaf_coa
    )
    if date:
        journal_entries = journal_entries.filter(transaction_date__lte=date)
    return journal_entries


def get_all_raw_data_with_date_range(account_name, level, start_date, end_date):
    all_coas = ChartofAccount.objects.filter(
        account_name__icontains=account_name, level=level
    )
    all_leaf_coa = get_all_child_coas(all_coas)
    journal_entries = JournalEntryTransaction.objects.filter(
        chart_of_account__in=all_leaf_coa,
        transaction_date__range=[start_date, end_date],
    )
    return journal_entries
