import time
from datetime import datetime
from decimal import Decimal

from accounting.models import (
    ChartofAccount,
    Entity,
    JournalEntry,
    JournalEntryTransaction,
)
from accounting.query_helper import (
    get_all_child_coas,
    get_all_raw_data_snapshot,
    get_all_raw_data_with_date_range,
)
from accounting.serializers import (
    ChartofAccountSerializer,
    EntitySerializer,
    JournalEntrySerializer,
    JournalEntryTransactionSerializer,
)
from dateutil.relativedelta import relativedelta
from django.db import models
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny
from rest_framework.viewsets import ModelViewSet
from utils.custom_pagination import CustomPagination
from utils.response_template import custom_error_response, custom_success_response


class EntityViewSet(ModelViewSet):
    queryset = Entity.objects.all()
    serializer_class = EntitySerializer
    lookup_field = "id"


class ChartofAccountViewSet(ModelViewSet):
    queryset = ChartofAccount.objects.all()
    serializer_class = ChartofAccountSerializer
    lookup_field = "id"
    permission_classes = [AllowAny]
    pagination_class = CustomPagination


class JournalEntryViewSet(ModelViewSet):
    queryset = JournalEntry.objects.all()
    serializer_class = JournalEntrySerializer
    lookup_field = "id"
    permission_classes = [AllowAny]
    pagination_class = CustomPagination


class JournalEntryTransactionViewSet(ModelViewSet):
    queryset = JournalEntryTransaction.objects.all()
    serializer_class = JournalEntryTransactionSerializer
    lookup_field = "id"
    permission_classes = [AllowAny]
    pagination_class = CustomPagination

    # @action(detail=False, methods=["get"], url_path="cash-accounts")
    # def cash_accounts(self, request):
    #     start_date = request.query_params.get("start_date")
    #     end_date = request.query_params.get("end_date")
    #     chart_of_accounts = ChartofAccount.objects.filter(
    #         cash_account=True
    #     ).values_list("id", flat=True)
    #     journal_entries = JournalEntryTransaction.objects.filter(
    #         chart_of_account__id__in=chart_of_accounts,
    #     )
    #     if start_date and end_date:
    #         journal_entries = journal_entries.filter(
    #             transaction_date__range=[start_date, end_date]
    #         )
    #     page = self.paginate_queryset(journal_entries)
    #     if page is not None:
    #         serializer = self.get_serializer(page, many=True)
    #         return self.get_paginated_response(serializer.data)
    #     serializer = self.get_serializer(journal_entries, many=True)
    #     return custom_success_response(data=serializer.data)

    # @action(detail=False, methods=["get"], url_path="cash-accounts-total")
    # def cash_accounts_total(self, request):
    #     start_date = request.query_params.get("start_date")
    #     end_date = request.query_params.get("end_date")
    #     chart_of_accounts = ChartofAccount.objects.filter(
    #         cash_account=True
    #     ).values_list("id", flat=True)
    #     journal_entries = JournalEntryTransaction.objects.filter(
    #         chart_of_account__id__in=chart_of_accounts,
    #     )
    #     if start_date and end_date:
    #         journal_entries = journal_entries.filter(
    #             transaction_date__range=[start_date, end_date]
    #         )
    #     total_debit = (
    #         journal_entries.aggregate(total_debit=models.Sum("reporting_debit_amount"))[
    #             "total_debit"
    #         ]
    #         or 0
    #     )
    #     total_credit = (
    #         journal_entries.aggregate(
    #             total_credit=models.Sum("reporting_credit_amount")
    #         )["total_credit"]
    #         or 0
    #     )
    #     total_amount = total_debit - total_credit
    #     return custom_success_response(data={"total_amount": total_amount})

    @action(detail=False, methods=["get"], url_path="filter-by-account")
    def filter_by_account(self, request):
        account_type = request.query_params.get("account")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")
        chart_of_accounts = ChartofAccount.objects.filter(
            account_type=account_type,
        ).values_list("id", flat=True)

        if account_type == "Revenues":
            chart_of_accounts = ChartofAccount.objects.filter(
                models.Q(account_type="Revenues") | models.Q(account_type="Income")
            ).values_list("id", flat=True)
        journal_entries = (
            JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=chart_of_accounts,
                transaction_date__range=[start_date, end_date],
            )
            .select_related("chart_of_account")
            .annotate(
                coa_account_code=models.F("chart_of_account__account_code"),
                account_name=models.F("chart_of_account__account_name"),
                clinic_code=models.F("chart_of_account__clinic_code"),
                clinic_name=models.F("chart_of_account__clinic_name"),
            )
        )
        page = self.paginate_queryset(journal_entries)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(journal_entries, many=True)
        return custom_success_response(data=serializer.data)

    @action(detail=False, methods=["get"], url_path="total-by-account")
    def total_by_account(self, request):
        account_type = request.query_params.get("account")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")
        chart_of_accounts = ChartofAccount.objects.filter(
            account_type=account_type,
        )
        journal_entries = JournalEntryTransaction.objects.filter(
            chart_of_account__in=chart_of_accounts,
            transaction_date__range=[start_date, end_date],
        ).values_list("reporting_debit_amount", "reporting_credit_amount")
        total_debit = sum(entry[0] for entry in journal_entries)
        total_credit = sum(entry[1] for entry in journal_entries)
        total_amount = total_debit - total_credit
        return custom_success_response(data={"total_amount": total_amount})

    @action(detail=False, methods=["get"], url_path="sap-income-statement")
    def get_income_statement(self, request):
        print("starting income statement breakdown")
        start_time = time.time()
        # Get optional date range parameters
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        # Get SAP entities in a single query
        sap_entities = Entity.objects.exclude(name__iexact="xero").exclude(
            name__iexact="autocount"
        )

        # Define account types and their display names
        account_types = {
            "Revenues": "Revenue",
            "Cost of sales": "Cost of Goods Sold",
            "Expenses": "Operating Expenses",
        }

        # Get all relevant level 3 COAs in a single query
        all_lvl3_coas = ChartofAccount.objects.filter(
            entity__in=sap_entities,
            account_type__in=list(account_types.keys()),
            level=3,
        ).select_related("entity")

        # Create a base query for all transactions
        base_query = JournalEntryTransaction.objects.filter(
            chart_of_account__parent__in=all_lvl3_coas
        )

        # Apply date filtering if provided
        if start_date and end_date:
            base_query = base_query.filter(
                transaction_date__range=[start_date, end_date]
            )

        # Get all results in a single query with proper annotations
        all_results = (
            base_query.values(
                "chart_of_account__parent",
                "chart_of_account__parent__account_name",
                "chart_of_account__parent__account_type",
            )
            .annotate(
                net_amount=models.Case(
                    models.When(
                        chart_of_account__parent__account_type="Revenues",
                        then=models.Sum("reporting_credit_amount")
                        - models.Sum("reporting_debit_amount"),
                    ),
                    default=models.Sum("reporting_debit_amount")
                    - models.Sum("reporting_credit_amount"),
                    output_field=models.DecimalField(),
                )
            )
            .order_by(
                "chart_of_account__parent__account_type",
                "chart_of_account__parent__account_name",
            )
        )

        # Organize results by account type
        results_by_type = {}
        for result in all_results:
            account_type = result["chart_of_account__parent__account_type"]
            if account_type not in results_by_type:
                results_by_type[account_type] = []
            results_by_type[account_type].append(result)

        # Build the response data
        response_data = []

        # Process each account type
        for account_type, display_name in account_types.items():
            category_data = {"category": display_name, "total": 0, "breakdown": []}

            # Skip if no data for this account type
            if account_type not in results_by_type:
                response_data.append(category_data)
                continue

            # Process results for this account type
            total_amount = 0

            # Create a dictionary to combine subcategories with the same name
            subcategory_totals = {}

            for result in results_by_type[account_type]:
                subcategory_amount = result["net_amount"] or 0
                coa_name = result["chart_of_account__parent__account_name"]

                # Add or update the amount for this subcategory name
                if coa_name in subcategory_totals:
                    subcategory_totals[coa_name] += subcategory_amount
                else:
                    subcategory_totals[coa_name] = subcategory_amount

                # Add to total amount
                total_amount += subcategory_amount

            # Create the breakdown from the combined subcategories
            for subcategory_name, amount in sorted(subcategory_totals.items()):
                category_data["breakdown"].append(
                    {"sub_category": subcategory_name, "amount": str(amount)}
                )

            # Set the total
            category_data["total"] = str(total_amount)
            response_data.append(category_data)

        # Calculate revenue, COGS, and operating expenses totals from the response data

        revenue_total = Decimal("0")
        cogs_total = Decimal("0")
        opex_total = Decimal("0")

        for category in response_data:
            if category["category"] == "Revenue":
                revenue_total = (
                    Decimal(category["total"]) if category["total"] else Decimal("0")
                )
            elif category["category"] == "Cost of Goods Sold":
                cogs_total = (
                    Decimal(category["total"]) if category["total"] else Decimal("0")
                )
            elif category["category"] == "Operating Expenses":
                opex_total = (
                    Decimal(category["total"]) if category["total"] else Decimal("0")
                )

        # Calculate operating income
        operating_income = revenue_total - cogs_total - opex_total

        # EBITDA calculation - optimized to use a single query with conditional aggregation
        da_accounts = ChartofAccount.objects.filter(
            models.Q(account_name__icontains="depreciation")
            | models.Q(account_name__icontains="amortization")
            | models.Q(account_name__icontains="amortisation")
        )

        # Base query for D&A transactions
        da_query = JournalEntryTransaction.objects.filter(
            chart_of_account__in=da_accounts
        )

        # Apply date filtering if provided
        if start_date and end_date:
            da_query = da_query.filter(transaction_date__range=[start_date, end_date])

        # Get aggregated results with conditional expressions to separate depreciation and amortization
        da_results = da_query.aggregate(
            # Depreciation calculations
            depreciation_debit=models.Sum(
                "reporting_debit_amount",
                filter=models.Q(
                    chart_of_account__account_name__icontains="depreciation"
                ),
            ),
            depreciation_credit=models.Sum(
                "reporting_credit_amount",
                filter=models.Q(
                    chart_of_account__account_name__icontains="depreciation"
                ),
            ),
            # Amortization calculations (including both spellings)
            amortization_debit=models.Sum(
                "reporting_debit_amount",
                filter=models.Q(
                    chart_of_account__account_name__icontains="amortization"
                )
                | models.Q(chart_of_account__account_name__icontains="amortisation"),
            ),
            amortization_credit=models.Sum(
                "reporting_credit_amount",
                filter=models.Q(
                    chart_of_account__account_name__icontains="amortization"
                )
                | models.Q(chart_of_account__account_name__icontains="amortisation"),
            ),
        )

        # Handle potential NULL values from the database
        depreciation_debit = da_results["depreciation_debit"] or 0
        depreciation_credit = da_results["depreciation_credit"] or 0
        amortization_debit = da_results["amortization_debit"] or 0
        amortization_credit = da_results["amortization_credit"] or 0

        # Calculate net D&A values - ensure they are negative (expenses)
        # For depreciation and amortization, typically debit increases (expense) and credit decreases
        # So we need to negate the result to get a negative value representing an expense
        depreciation_total = -(depreciation_debit - depreciation_credit)
        amortization_total = -(amortization_debit - amortization_credit)

        # Calculate EBITDA: Operating Income + Depreciation + Amortization
        # Since depreciation and amortization are now negative, we're effectively adding them back
        ebitda_total = operating_income + depreciation_total + amortization_total

        category_data = {
            "category": "EBITDA",
            "total": str(ebitda_total),
            "breakdown": [
                {"sub_category": "Operating Income", "amount": str(operating_income)},
                {"sub_category": "Depreciation", "amount": str(depreciation_total)},
                {"sub_category": "Amortization", "amount": str(amortization_total)},
            ],
        }

        response_data.append(category_data)
        print("income statement calculation time: ", time.time() - start_time)

        return custom_success_response(data=response_data)

    @action(detail=False, methods=["get"], url_path="cash-position")
    def get_cash_position(self, request):
        print("staring cash position calculation")
        date = request.query_params.get("date")
        if not date:
            date = datetime.now().strftime("%Y-%m-%d")

        # Convert date string to datetime object
        current_date = datetime.strptime(date, "%Y-%m-%d")

        # Calculate one month ago date
        month_ago_date = current_date - relativedelta(months=1)
        month_ago_date_str = month_ago_date.strftime("%Y-%m-%d")

        # Calculate beginning of year date
        start_of_year_date = datetime(current_date.year, 1, 1)
        start_of_year_date_str = start_of_year_date.strftime("%Y-%m-%d")

        # Get cash accounts
        cash_account_coas = ChartofAccount.objects.filter(cash_account=True)

        # Current cash position
        current_journal_entries = JournalEntryTransaction.objects.filter(
            chart_of_account__in=cash_account_coas, transaction_date__lte=date
        ).values_list("reporting_debit_amount", "reporting_credit_amount")

        current_total_debit = sum(entry[0] for entry in current_journal_entries)
        current_total_credit = sum(entry[1] for entry in current_journal_entries)
        current_amount = (current_total_debit - current_total_credit) * -1

        # Month ago cash position
        month_ago_journal_entries = JournalEntryTransaction.objects.filter(
            chart_of_account__in=cash_account_coas,
            transaction_date__lte=month_ago_date_str,
        ).values_list("reporting_debit_amount", "reporting_credit_amount")

        month_ago_total_debit = sum(entry[0] for entry in month_ago_journal_entries)
        month_ago_total_credit = sum(entry[1] for entry in month_ago_journal_entries)
        month_ago_amount = (month_ago_total_debit - month_ago_total_credit) * -1

        # Year start cash position
        year_start_journal_entries = JournalEntryTransaction.objects.filter(
            chart_of_account__in=cash_account_coas,
            transaction_date__lte=start_of_year_date_str,
        ).values_list("reporting_debit_amount", "reporting_credit_amount")

        year_start_total_debit = sum(entry[0] for entry in year_start_journal_entries)
        year_start_total_credit = sum(entry[1] for entry in year_start_journal_entries)
        year_start_amount = (year_start_total_debit - year_start_total_credit) * -1

        # Calculate percentage changes
        month_on_month = 0
        if month_ago_amount != 0:
            month_on_month = (
                (current_amount - month_ago_amount) / abs(month_ago_amount)
            ) * 100

        year_to_date = 0
        if year_start_amount != 0:
            year_to_date = (
                (current_amount - year_start_amount) / abs(year_start_amount)
            ) * 100

        return custom_success_response(
            data={
                "total_amount": current_amount,
                "month_on_month": round(month_on_month, 2),
                "year_to_date": round(year_to_date, 2),
            }
        )

    @action(detail=False, methods=["get"], url_path="get-working-capital")
    def get_working_capital(self, request):
        start_time = time.time()
        print("starting working capital calculation")

        # Get date parameter
        date = request.query_params.get("date")
        if not date:
            date = datetime.now().strftime("%Y-%m-%d")

        # Convert date string to datetime object
        current_date = datetime.strptime(date, "%Y-%m-%d")

        # Calculate one month ago date
        month_ago_date = current_date - relativedelta(months=1)
        month_ago_date_str = month_ago_date.strftime("%Y-%m-%d")

        # Calculate beginning of year date
        start_of_year_date = datetime(current_date.year, 1, 1)
        start_of_year_date_str = start_of_year_date.strftime("%Y-%m-%d")

        # Get level 2 COAs for current assets and current liabilities
        current_asset_coas_lvl2 = ChartofAccount.objects.filter(
            account_name__icontains="current asset"
        ).exclude(account_name__icontains="non-current asset")

        current_liability_coas_lvl2 = ChartofAccount.objects.filter(
            account_name__icontains="current liabilit"
        ).exclude(account_name__icontains="non-current liabilit")

        # Get all descendant COAs
        all_current_asset_coas = get_all_child_coas(current_asset_coas_lvl2)
        all_current_liability_coas = get_all_child_coas(current_liability_coas_lvl2)

        # Get all COA IDs for filtering
        current_asset_coa_ids = [coa.id for coa in all_current_asset_coas]
        current_liability_coa_ids = [coa.id for coa in all_current_liability_coas]

        # Function to calculate working capital for a specific date
        def calculate_working_capital(cutoff_date):
            # Base query for transactions up to the cutoff date
            base_query = JournalEntryTransaction.objects.filter(
                transaction_date__lte=cutoff_date
            )

            # Calculate current assets total
            current_asset_transactions = base_query.filter(
                chart_of_account_id__in=current_asset_coa_ids
            )

            # For assets: debit increases, credit decreases
            current_asset_results = current_asset_transactions.aggregate(
                debit_total=models.Sum("reporting_debit_amount"),
                credit_total=models.Sum("reporting_credit_amount"),
            )

            # Handle NULL values
            current_asset_debit = current_asset_results["debit_total"] or Decimal("0")
            current_asset_credit = current_asset_results["credit_total"] or Decimal("0")
            current_asset_total = current_asset_debit - current_asset_credit

            # Calculate current liabilities total
            current_liability_transactions = base_query.filter(
                chart_of_account_id__in=current_liability_coa_ids
            )

            # For liabilities: credit increases, debit decreases
            current_liability_results = current_liability_transactions.aggregate(
                debit_total=models.Sum("reporting_debit_amount"),
                credit_total=models.Sum("reporting_credit_amount"),
            )

            # Handle NULL values
            current_liability_debit = current_liability_results[
                "debit_total"
            ] or Decimal("0")
            current_liability_credit = current_liability_results[
                "credit_total"
            ] or Decimal("0")
            current_liability_total = current_liability_credit - current_liability_debit

            # Calculate working capital
            working_capital = current_asset_total - current_liability_total

            return {
                "working_capital": working_capital,
                "current_assets": current_asset_total,
                "current_liabilities": current_liability_total,
            }

        # Calculate working capital for current date, month ago, and year start
        current_wc = calculate_working_capital(date)
        month_ago_wc = calculate_working_capital(month_ago_date_str)
        year_start_wc = calculate_working_capital(start_of_year_date_str)

        # Calculate percentage changes
        month_on_month = Decimal("0")
        if month_ago_wc["working_capital"] != 0:
            month_on_month = (
                (current_wc["working_capital"] - month_ago_wc["working_capital"])
                / abs(month_ago_wc["working_capital"])
            ) * 100

        year_to_date = Decimal("0")
        if year_start_wc["working_capital"] != 0:
            year_to_date = (
                (current_wc["working_capital"] - year_start_wc["working_capital"])
                / abs(year_start_wc["working_capital"])
            ) * 100

        # Prepare response data with breakdown and trend analysis
        response_data = {
            "working_capital": str(current_wc["working_capital"]),
            "month_on_month": round(month_on_month, 2),
            "year_to_date": round(year_to_date, 2),
            "breakdown": {
                "current_assets": str(current_wc["current_assets"]),
                "current_liabilities": str(current_wc["current_liabilities"]),
            },
            "historical": {
                "month_ago": str(month_ago_wc["working_capital"]),
                "year_start": str(year_start_wc["working_capital"]),
            },
        }

        print("working capital calculation time: ", time.time() - start_time)
        return custom_success_response(data=response_data)
