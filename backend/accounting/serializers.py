from rest_framework import serializers
from accounting.models import (
    Entity,
    JournalEntry,
    JournalEntryTransaction,
    ChartofAccount,
)


class EntitySerializer(serializers.ModelSerializer):
    class Meta:
        model = Entity
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at")


class ChartofAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChartofAccount
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at")


class JournalEntrySerializer(serializers.ModelSerializer):
    class Meta:
        model = JournalEntry
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at")


class JournalEntryTransactionSerializer(serializers.ModelSerializer):
    coa_account_code = serializers.CharField(read_only=True, required=False)
    account_name = serializers.CharField(read_only=True, required=False)
    clinic_code = serializers.Char<PERSON>ield(read_only=True, required=False)
    clinic_name = serializers.Char<PERSON>ield(read_only=True, required=False)

    class Meta:
        model = JournalEntryTransaction
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at")
