export interface IncomeStatementSubCategory {
  sub_category: string
  amount: number
}

export interface IncomeStatementCategory {
  category: string
  total: number
  breakdown: IncomeStatementSubCategory[]
}

export interface CashPosition {
  total_amount: number
  month_on_month: number
  year_to_date: number
}

export interface WorkingCapital {
  working_capital: number
  month_on_month: number
  year_to_date: number
  breakdown: {
    current_assets: number
    current_liabilities: number
  }
  historical: {
    month_ago: string
    year_start: string
  }
}
