"use client"

import React, { useRef, useState } from "react"
import { FileText } from "lucide-react"
import {
  <PERSON>,
  <PERSON>hart,
  CartesianGrid,
  Cell,
  ComposedChart,
  // DefaultTooltipContent,
  LabelList,
  Legend,
  Line,
  Pie,
  <PERSON><PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  // TooltipProps,
  Treemap,
  XAxis,
  YAxis,
} from "recharts"
import {
  // NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent"
import { TickItem } from "recharts/types/util/types"

import { formatCurrency } from "@/lib/number"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import Overview from "@/components/business/Overview"
import PeriodSelector from "@/components/business/PeriodSelector"
import DrillDownModal from "@/components/drill-down/DrillDownModal"
import {
  CardContainer,
  CardTitleWithIcon,
} from "@/components/summary/Components"
import ExportPageButton from "@/components/summary/ExportPageButton"
import { Breadcrumb } from "@/contexts/breadcrumb"
import { useRevenue } from "@/services/finance"

// Sample data for Revenue vs Target chart
const revenueTargetData = [
  { month: "JUL", actual: 9800, target: 10000, forecast: 9550 },
  { month: "AUG", actual: 9200, target: 10000, forecast: 9300 },
  { month: "SEP", actual: 10100, target: 10000, forecast: 10200 },
  { month: "OCT", actual: 8900, target: 10000, forecast: 9100 },
  { month: "NOV", actual: 9600, target: 10000, forecast: 9700 },
  { month: "DEC", actual: 9800, target: 10000, forecast: 9900 },
  { month: "JAN", actual: 10200, target: 10000, forecast: 10300 },
  { month: "FEB", actual: 9700, target: 10000, forecast: 9800 },
  { month: "MAR", actual: 10400, target: 10000, forecast: 10500 },
  { month: "APR", actual: 10100, target: 10000, forecast: 10200 },
  { month: "MAY", actual: 10600, target: 10000, forecast: 10700 },
]

// Sample data for Revenue per Employee chart
const revenuePerEmployeeData = [
  { month: "JUL", sales: 8500, avgRevenue: 7200, growthRate: 5.2 },
  { month: "AUG", sales: 8200, avgRevenue: 7100, growthRate: 4.8 },
  { month: "SEP", sales: 8800, avgRevenue: 7400, growthRate: 6.1 },
  { month: "OCT", sales: 8100, avgRevenue: 7000, growthRate: 4.5 },
  { month: "NOV", sales: 8600, avgRevenue: 7300, growthRate: 5.8 },
  { month: "DEC", sales: 8900, avgRevenue: 7500, growthRate: 6.3 },
  { month: "JAN", sales: 9200, avgRevenue: 7700, growthRate: 6.8 },
  { month: "FEB", sales: 8700, avgRevenue: 7400, growthRate: 5.9 },
  { month: "MAR", sales: 9100, avgRevenue: 7600, growthRate: 6.5 },
  { month: "APR", sales: 9400, avgRevenue: 7800, growthRate: 7.1 },
  { month: "MAY", sales: 9600, avgRevenue: 8000, growthRate: 7.4 },
  { month: "JUN", sales: 9800, avgRevenue: 8200, growthRate: 7.8 },
]

// Sample data for Headcount donut chart
const headcountData = [
  { name: "Male", value: 74, color: "var(--chart-1)" },
  { name: "Female", value: 52, color: "var(--chart-3)" },
]

const Business = () => {
  const contentRef = useRef<HTMLDivElement>(null)

  return (
    <>
      <Breadcrumb links={[{ label: "Business" }]} />

      <div className="flex flex-wrap justify-between gap-4">
        <PeriodSelector />
        <ExportPageButton fileName="Business Report" contentRef={contentRef} />
      </div>

      <div ref={contentRef} className="flex flex-col gap-4">
        <Overview />

        <Card className="non-breakable">
          <CardTitleWithIcon icon={FileText} title="Key Summary" />

          <CardContainer>
            <p className="text-sm">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
              enim ad minim veniam, quis nostrud exercitation ullamco laboris
              nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in
              reprehenderit in voluptate velit esse cillum dolore eu fugiat
              nulla pariatur. Excepteur sint occaecat cupidatat non proident,
              sunt in culpa qui officia deserunt mollit anim id est laborum.
            </p>

            <p className="text-sm">
              Sed ut perspiciatis unde omnis iste natus error sit voluptatem
              accusantium doloremque laudantium, totam rem aperiam, eaque ipsa
              quae ab illo inventore veritatis et quasi architecto beatae vitae
              dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit
              aspernatur aut odit aut fugit, sed quia consequuntur magni dolores
              eos qui ratione voluptatem sequi nesciunt.
            </p>
          </CardContainer>
        </Card>

        {/* Charts Section */}
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-5">
          {/* Left column: spans 2 columns on medium+ screens */}
          <div className="grid gap-4 lg:col-span-3">
            <RevenueVsTargetChart />
            <RevenuePerEmployeeChart />
          </div>

          {/* Right column: single column layout */}
          <div className="grid gap-4 lg:col-span-2">
            <IncomeStatementChart />
            <HeadcountChart />
            <TreemapCard />
          </div>
        </div>
      </div>
    </>
  )
}

const RevenueVsTargetChart = () => {
  const [open, setOpen] = useState<boolean>(false)

  const { data = [] } = useRevenue()

  return (
    <>
      <Card className="non-breakable">
        <CardContent className="flex flex-col gap-4">
          <CardTitle className="text-center">Revenue vs Target</CardTitle>

          <ResponsiveContainer width="99%" height={350}>
            <BarChart
              data={revenueTargetData}
              style={{ cursor: data.length > 0 ? "pointer" : "default" }}
              onClick={() => {
                if (data.length > 0) {
                  setOpen(true)
                }
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" tick={{ fontSize: 12 }} />
              <YAxis
                tickFormatter={(value) => `$${formatCurrency(value, 0)}`}
                tick={{ fontSize: 12 }}
              />
              <Tooltip
                formatter={(value: number) => `$${formatCurrency(value)}`}
              />
              <Legend verticalAlign="bottom" wrapperStyle={{ fontSize: 12 }} />
              <Bar dataKey="actual" name="Actual" fill="var(--chart-1)" />
              <Bar dataKey="forecast" name="Forecast" fill="var(--chart-3)" />
              <Bar dataKey="target" name="Target" fill="var(--chart-5)" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <DrillDownModal
        title="Revenue vs Target"
        data={data}
        open={open}
        setOpen={setOpen}
      />
    </>
  )
}

const incomeStatementData = [
  {
    name: "Total<br/>Revenue",
    amount: 108325,
    remaining: 0,
  },
  {
    name: "Cost of Goods<br/>Sold",
    amount: -77305,
    remaining: 108325,
  },
  {
    name: "Total<br/>Operating<br/>Expenses",
    amount: -13507,
    remaining: 31020,
  },
  {
    name: "Non-Opr.<br/>Income<br/>(Expense)",
    amount: 844,
    remaining: 17513,
  },
  {
    name: "Finance<br/>Expense",
    amount: -4389,
    remaining: 18357,
  },
  {
    name: "Net Profit<br/>Before Tax",
    amount: 13968,
    remaining: 0,
  },
]

const IncomeStatementChart = () => (
  <Card className="non-breakable">
    <CardContent className="flex flex-col gap-4">
      <CardTitle className="text-center">Income Statement</CardTitle>

      <ResponsiveContainer width="99%" height={200}>
        <BarChart data={incomeStatementData}>
          <XAxis dataKey="name" tick={WrappedTick} interval={0} />
          {/* <Tooltip
            content={<CustomTooltip />}
            labelFormatter={(label) => label.replace(/<br\/>/g, " ")}
          /> */}
          <Bar dataKey="remaining" stackId="a" fill="transparent" />
          <Bar dataKey="amount" name="Amount" stackId="a" fill="var(--chart-1)">
            {incomeStatementData.map((item, index) => {
              if (item.amount < 0) {
                return <Cell key={index} fill="var(--chart-3)" />
              }

              if (item.name === "Net Profit<br/>Before Tax") {
                return <Cell key={index} fill="var(--chart-5)" />
              }

              return <Cell key={index} fill="var(--chart-1)" />
            })}

            <LabelList
              dataKey="amount"
              position="top"
              formatter={(value: number) =>
                `$${formatCurrency(Math.abs(value), 0)}`
              }
              fill="var(--foreground)"
              fontSize={12}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </CardContent>
  </Card>
)

const WrappedTick = ({
  x,
  y,
  payload,
}: {
  x?: number
  y?: number
  payload: TickItem & { value: ValueType }
}) => {
  const words = payload.value.split("<br/>")

  return (
    <g transform={`translate(${x},${(y || 0) + 10})`}>
      <text textAnchor="middle" fontSize={10} fill="var(--muted-foreground)">
        {words.map((word: string, index: number) => (
          <tspan key={index} x={0} dy={index === 0 ? 0 : 12}>
            {word}
          </tspan>
        ))}
      </text>
    </g>
  )
}

// const CustomTooltip = (props: TooltipProps<ValueType, NameType>) => {
//   if (!props.active) return null

//   const amount = props.payload?.[0]?.payload?.amount || 0

//   const payload = [
//     {
//       name: "Amount",
//       value: `$${formatCurrency(Math.abs(amount))}`,
//       color:
//         props.payload?.[0]?.payload?.name === "Net Profit<br/>Before Tax"
//           ? "var(--chart-5)"
//           : amount < 0
//             ? "var(--chart-3)"
//             : "var(--chart-1)",
//     },
//   ]

//   return <DefaultTooltipContent {...props} payload={payload} />
// }

const RevenuePerEmployeeChart = () => (
  <Card className="non-breakable">
    <CardContent className="flex flex-col gap-4">
      <CardTitle className="text-center">Revenue per Employee</CardTitle>

      <ResponsiveContainer width="99%" height={350}>
        <ComposedChart data={revenuePerEmployeeData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" tick={{ fontSize: 12 }} />
          <YAxis
            yAxisId="left"
            tickFormatter={(value) => `$${formatCurrency(value, 0)}`}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            tickFormatter={(value) => `${value}%`}
            tick={{ fontSize: 12 }}
          />
          <Tooltip
            formatter={(value: number, name: string) => {
              if (name === "Growth Rate") return [`${value}%`, name]
              return [`$${formatCurrency(value)}`, name]
            }}
          />
          <Legend verticalAlign="bottom" wrapperStyle={{ fontSize: 12 }} />
          <Bar
            yAxisId="left"
            dataKey="sales"
            name="Sales"
            fill="var(--chart-1)"
          />
          <Bar
            yAxisId="left"
            dataKey="avgRevenue"
            name="Avg Revenue per Employee"
            fill="var(--chart-3)"
          />
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="growthRate"
            name="Growth Rate"
            stroke="var(--chart-5)"
            strokeWidth={2}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </CardContent>
  </Card>
)

// Headcount Chart Component
const HeadcountChart = () => (
  <Card className="non-breakable">
    <CardContent className="flex flex-col gap-4">
      <CardTitle className="text-center">Headcount</CardTitle>

      <ResponsiveContainer width="99%" height={200}>
        <PieChart>
          <Pie
            data={headcountData}
            dataKey="value"
            nameKey="name"
            cx="50%"
            cy="50%"
            innerRadius={30}
            outerRadius={50}
            labelLine={false}
            label={({ name, percent }) =>
              `${name} (${(percent * 100).toFixed(0)}%)`
            }
          >
            {headcountData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip />
          <Legend verticalAlign="bottom" wrapperStyle={{ fontSize: 12 }} />
        </PieChart>
      </ResponsiveContainer>
    </CardContent>
  </Card>
)

interface TreemapChild {
  name: string
  size: number
}

interface TreemapItem {
  name: string
  size: number
  children?: TreemapChild[]
}

interface TreemapNode {
  x: number
  y: number
  width: number
  height: number
  name: string
  size?: number
  value?: number
  depth: number
  payload?: TreemapItem
}

const TreemapCard = () => {
  const treemapData: TreemapItem[] = [
    {
      name: "Retail & Services",
      size: 1638.4,
      children: [
        { name: "Retail & Services", size: 675.2 },
        { name: "High Tech", size: 141.0 },
        { name: "Life Sciences", size: 107.4 },
      ],
    },
    { name: "Manufacturing", size: 363.6 },
    { name: "Healthcare", size: 300.2 },
    { name: "Public Sector", size: 232.1 },
    { name: "Financial Services", size: 204.9 },
  ]

  const COLORS = [
    "var(--chart-1)",
    "var(--chart-2)",
    "var(--chart-3)",
    "var(--chart-4)",
    "var(--chart-5)",
  ]

  const CustomizedContent = (props: TreemapNode) => {
    const { x, y, width, height, name, size, depth } = props
    const value = size || props.value
    const color = COLORS[depth % COLORS.length]

    return (
      <g>
        <rect
          x={x}
          y={y}
          width={width}
          height={height}
          style={{
            fill: color,
            stroke: "#fff",
            strokeWidth: 2,
          }}
        />
        {width > 60 && height > 32 && (
          <text x={x + 8} y={y + 20} fontSize={12} fill="#fff">
            {name}
          </text>
        )}
        {width > 60 && height > 32 && (
          <text x={x + 8} y={y + 38} fontSize={12} fill="#fff">
            ${value?.toLocaleString()}
          </text>
        )}
      </g>
    )
  }

  return (
    <Card className="non-breakable">
      <CardContent className="flex flex-col gap-4">
        <CardTitle className="text-center">Service Breakdown</CardTitle>

        <ResponsiveContainer width="99%" height={200}>
          <Treemap
            isAnimationActive={false}
            data={treemapData}
            dataKey="size"
            stroke="#fff"
            content={React.createElement(CustomizedContent)}
          />
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

export default Business
