"use client"

import React, { useEffect, useRef, useState } from "react"
import {
  Banknote,
  BarChart2,
  DollarSign,
  Percent,
  TrendingDown,
} from "lucide-react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  Treemap,
  XAxis,
  YAxis,
} from "recharts"

import { CashPosition, IncomeStatementCategory } from "@/types/finance"
import { formatAbbreviatedCurrency, formatCurrency } from "@/lib/number"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import ExportPageButton from "@/components/summary/ExportPageButton"
import { Breadcrumb } from "@/contexts/breadcrumb"
import {
  useCashPosition,
  useIncomeStatementBreakdown,
} from "@/services/finance"

const Financial = () => {
  const contentRef = useRef<HTMLDivElement>(null)

  const [incomeStatementBreakdown, setIncomeStatementBreakdown] = useState<
    IncomeStatementCategory[]
  >([])

  const [cashPosition, setCashPosition] = useState<CashPosition>({
    total_amount: 0,
    month_on_month: 0,
    year_to_date: 0,
  })

  const { data, isLoading } = useIncomeStatementBreakdown()

  const { data: cashPositionData } = useCashPosition()

  useEffect(() => {
    if (data) {
      setIncomeStatementBreakdown(data.data)
    }
  }, [data])

  useEffect(() => {
    if (cashPositionData) {
      setCashPosition(cashPositionData.data)
    }
  }, [cashPositionData])

  return (
    <>
      <Breadcrumb links={[{ label: "Financial" }]} />

      <div className="flex flex-wrap justify-between gap-4">
        <Filters />
        <ExportPageButton fileName="Financial Report" contentRef={contentRef} />
      </div>

      <div ref={contentRef} className="flex flex-col gap-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <MetricCard
            title="Cash Position"
            value={formatAbbreviatedCurrency(cashPosition.total_amount)}
            momChange={`${cashPosition.month_on_month.toString()}%`}
            ytdChange={`${cashPosition.year_to_date.toString()}%`}
            description="Cash position strengthened by 8.2% MoM, driven by improved collections and working capital management."
            icon={<DollarSign className="text-primary size-4" />}
            iconColor="bg-primary/10"
            progressPercent={93}
            isPositive
          />

          <MetricCard
            title="Working Capital"
            value="$2.9M"
            momChange="5.4%"
            ytdChange="12.8%"
            description="Working capital efficiency improved. Inventory turnover increased to 8.2x."
            icon={<BarChart2 className="text-primary size-4" />}
            iconColor="bg-primary/10"
            progressPercent={95}
            isPositive
          />

          <MetricCard
            title="Accounts Receivable"
            value="$1.9M"
            momChange="3.2%"
            ytdChange="8.5%"
            description="AR decreased through improved collection. DSO reduced to 38 days."
            icon={<TrendingDown className="text-primary size-4" />}
            iconColor="bg-primary/10"
            progressPercent={109}
            isPositive={false}
          />

          <MetricCard
            title="Accounts Payable"
            value="$1.3M"
            momChange="2.1%"
            ytdChange="5.4%"
            description="AP well-managed with strategic payment timing. Discounts worth $25K captured."
            icon={<Banknote className="text-primary size-4" />}
            iconColor="bg-primary/10"
            progressPercent={96}
            isPositive
          />

          <MetricCard
            title="Long-term Bank Debt"
            value="$4.8M"
            momChange="2.1%"
            ytdChange="8.4%"
            description="Debt reduced ahead of schedule. Coverage ratio improved to 3.2x."
            icon={<Banknote className="text-primary size-4" />}
            iconColor="bg-primary/10"
            progressPercent={107}
            isPositive={false}
          />

          <MetricCard
            title="Debt-to-Equity Ratio"
            value="42%"
            momChange="1.2%"
            ytdChange="4.5%"
            description="Improved to 0.42, approaching target. Credit rating outlook positive."
            icon={<Percent className="text-primary size-4" />}
            iconColor="bg-primary/10"
            progressPercent={105}
            isPositive
          />
        </div>

        <div className="grid grid-cols-1 items-start gap-4 lg:grid-cols-3">
          <IncomeStatementCard
            incomeStatementBreakdown={incomeStatementBreakdown}
            isLoading={isLoading}
          />

          <div className="flex flex-col gap-4 lg:col-span-2">
            <OperatingIncomeTrendCard />
            <CashFlowCard />
            <TreemapCard />
          </div>
        </div>
      </div>
    </>
  )
}

export default Financial

const Filters = () => (
  <div className="flex items-center gap-3">
    <Select defaultValue="all-locations">
      <SelectTrigger className="bg-card w-full max-w-40">
        <SelectValue placeholder="All Locations" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all-locations">All Locations</SelectItem>
        <SelectItem value="north">North Region</SelectItem>
        <SelectItem value="south">South Region</SelectItem>
        <SelectItem value="east">East Region</SelectItem>
        <SelectItem value="west">West Region</SelectItem>
      </SelectContent>
    </Select>

    <Button>Apply</Button>
  </div>
)

type MetricCardProps = {
  title: string
  value: string
  momChange: string
  ytdChange: string
  description: string
  icon: React.ReactNode
  iconColor: string
  progressPercent: number
  progressColor?: string
  isPositive?: boolean
}

const MetricCard = ({
  title,
  value,
  momChange,
  ytdChange,
  description,
  icon,
  iconColor,
  progressPercent,
  isPositive = true,
}: MetricCardProps) => (
  <Card className="non-breakable">
    <CardContent className="flex flex-1 flex-col gap-2">
      <div className="flex items-start justify-between">
        <div className="flex flex-col gap-1">
          <CardDescription>{title}</CardDescription>
          <CardTitle className="text-2xl tabular-nums">{value}</CardTitle>
        </div>
        <div className={cn("p-2", iconColor)}>{icon}</div>
      </div>

      <div className="flex items-center gap-2 text-sm">
        <span
          className={cn(
            "text-xs",
            isPositive ? "text-green-600" : "text-red-600"
          )}
        >
          {isPositive ? "▲" : "▼"} {momChange} MoM
        </span>
        <span
          className={cn(
            "text-xs",
            isPositive ? "text-green-600" : "text-red-600"
          )}
        >
          {isPositive ? "▲" : "▼"} {ytdChange} YTD
        </span>
      </div>

      <p className="text-muted-foreground text-xs">{description}</p>

      <div className="mt-auto flex items-center justify-between">
        <Progress value={progressPercent} className="h-2 flex-1" />
        <span className="text-muted-foreground ml-2 text-xs tabular-nums">
          {progressPercent}%
        </span>
      </div>
    </CardContent>
  </Card>
)

const IncomeStatementCard = ({
  incomeStatementBreakdown,
  isLoading,
}: {
  incomeStatementBreakdown: IncomeStatementCategory[]
  isLoading: boolean
}) => {
  return (
    <Card className="non-breakable">
      <CardContent>
        <div className="mb-4 text-lg font-bold">
          Income Statement Breakdown for 2024
        </div>

        {isLoading ? (
          <Skeleton className="h-24" />
        ) : (
          incomeStatementBreakdown.map((category) => {
            return (
              <div key={category.category}>
                <div className="mt-4 mb-2 flex items-center justify-between">
                  <div className="font-semibold">{category.category}</div>
                  <div className="font-bold">
                    {formatAbbreviatedCurrency(category.total)}
                  </div>
                </div>
                <div className="text-muted-foreground mb-4 ml-2 text-sm">
                  {category.breakdown.map((breakdown) => {
                    return (
                      <div
                        className="mb-2 flex justify-between gap-2"
                        key={breakdown.sub_category}
                      >
                        <span>{breakdown.sub_category}</span>
                        <span>
                          {formatAbbreviatedCurrency(breakdown.amount)}
                        </span>
                      </div>
                    )
                  })}
                </div>
              </div>
            )
          })
        )}

        {/* EBITDA Section */}
        <div className="bg-primary/10 mt-4 p-4">
          <div className="mb-2 flex items-center justify-between">
            <div className="font-semibold">EBITDA</div>
            <div className="font-bold">$1.6M</div>
          </div>
          <div className="bg-primary/10 mb-2 flex items-center justify-between px-2 py-1.5 text-sm">
            <span className="text-muted-foreground">Operating Income</span>
            <span className="font-semibold">$1.6M</span>
          </div>
          <div className="mb-2 flex items-center justify-between px-2 text-sm">
            <span className="text-muted-foreground">Depreciation</span>
            <span className="font-semibold text-red-600">- $150K</span>
          </div>
          <div className="mb-2 flex items-center justify-between px-2 text-sm">
            <span className="text-muted-foreground">Amortization</span>
            <span className="font-semibold text-red-600">- $80K</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

const OperatingIncomeTrendCard = () => {
  const data = [
    {
      month: "January",
      current: 1450,
      budget: 1550,
      previous: 1300,
    },
    {
      month: "February",
      current: 1500,
      budget: 1570,
      previous: 1350,
    },
    {
      month: "March",
      current: 1530,
      budget: 1590,
      previous: 1400,
    },
    {
      month: "April",
      current: 1560,
      budget: 1600,
      previous: 1450,
    },
    {
      month: "May",
      current: 1600,
      budget: 1620,
      previous: 1500,
    },
    {
      month: "June",
      current: 1620,
      budget: 1650,
      previous: 1550,
    },
  ]

  return (
    <Card className="non-breakable">
      <CardContent className="flex flex-col gap-4">
        <div className="text-primary bg-primary/10 p-4 text-sm">
          EBITDA margin expanded to 65.2%, reflecting operational leverage.
          18.3% MoM growth driven by revenue scaling and cost optimization.
          Depreciation impact remains stable at 9.4% of revenue.
        </div>

        <CardTitle className="text-center">
          Operating Income Trend Analysis
        </CardTitle>

        <ResponsiveContainer width="99%" height={300}>
          <ComposedChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" tick={{ fontSize: 12 }} />
            <YAxis tickFormatter={(v) => `$${formatCurrency(v, 0)}`} />
            <Tooltip
              formatter={(value: number) => `$${formatCurrency(value)}`}
            />
            <Legend verticalAlign="bottom" wrapperStyle={{ fontSize: 12 }} />
            <Bar
              dataKey="current"
              name="Current Year"
              fill="var(--chart-1)"
              barSize={32}
            />
            <Line
              type="monotone"
              dataKey="budget"
              name="Budget"
              stroke="var(--chart-3)"
              strokeWidth={3}
              dot={{ stroke: "var(--chart-3)" }}
            />
            <Line
              type="monotone"
              dataKey="previous"
              name="Previous Year"
              stroke="var(--chart-5)"
              strokeWidth={3}
              dot={{ stroke: "var(--chart-5)" }}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

interface CashFlowDataPoint {
  month: string
  inflow: number
  outflow: number
  net: number
}

const CashFlowCard = () => {
  const cashFlowData: CashFlowDataPoint[] = [
    { month: "JUL", inflow: 8500, outflow: 3362, net: 5138 },
    { month: "AUG", inflow: 8700, outflow: 4298, net: 4402 },
    { month: "SEP", inflow: 9900, outflow: 4901, net: 4099 },
    { month: "OCT", inflow: 8500, outflow: 10500, net: -2000 },
    { month: "NOV", inflow: 10900, outflow: 4750, net: 6150 },
    { month: "DEC", inflow: 8600, outflow: 4250, net: 4350 },
    { month: "JAN", inflow: 8734, outflow: 3866, net: 4868 },
    { month: "FEB", inflow: 9023, outflow: 4977, net: 4046 },
    { month: "MAR", inflow: 8500, outflow: 4650, net: 3850 },
    { month: "APR", inflow: 9701, outflow: 4299, net: 5402 },
    { month: "MAY", inflow: 8500, outflow: 4484, net: 4016 },
    { month: "JUN", inflow: 9900, outflow: 4592, net: 5308 },
  ]

  return (
    <Card className="non-breakable">
      <CardContent className="flex flex-col gap-4">
        <CardTitle className="text-center">Cash Flow</CardTitle>

        <ResponsiveContainer width="99%" height={300}>
          <ComposedChart data={cashFlowData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" tick={{ fontSize: 12 }} />

            <YAxis
              yAxisId="left"
              tickFormatter={(v) => `$${formatCurrency(v, 0)}`}
            />

            <YAxis
              yAxisId="right"
              orientation="right"
              tickFormatter={(v) => `$${formatCurrency(v, 0)}`}
            />

            <Tooltip formatter={(value: number) => formatCurrency(value)} />
            <Legend verticalAlign="bottom" wrapperStyle={{ fontSize: 12 }} />
            <Bar
              yAxisId="left"
              dataKey="inflow"
              name="Operating Cash Inflow"
              fill="var(--chart-1)"
              label={{
                position: "top",
                fill: "var(--foreground)",
                fontSize: 10,
                formatter: (v: number) => formatCurrency(v, 0),
              }}
            />
            <Bar
              yAxisId="left"
              dataKey="outflow"
              name="Operating Cash Outflow"
              fill="var(--chart-3)"
              label={{
                position: "top",
                fill: "var(--foreground)",
                fontSize: 10,
                formatter: (v: number) => formatCurrency(v, 0),
              }}
            />
            <Line
              yAxisId="right"
              type="monotone"
              dataKey="net"
              name="Net Operating Cash Flow"
              stroke="var(--chart-5)"
              strokeWidth={2}
              dot={{ stroke: "var(--chart-5)" }}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

interface TreemapChild {
  name: string
  size: number
}

interface TreemapItem {
  name: string
  size: number
  children?: TreemapChild[]
}

interface TreemapNode {
  x: number
  y: number
  width: number
  height: number
  name: string
  size?: number
  value?: number
  depth: number
  payload?: TreemapItem
}

const TreemapCard = () => {
  const treemapData: TreemapItem[] = [
    {
      name: "Retail & Services",
      size: 1638.4,
      children: [
        { name: "Retail & Services", size: 675.2 },
        { name: "High Tech", size: 141.0 },
        { name: "Life Sciences", size: 107.4 },
      ],
    },
    { name: "Manufacturing", size: 363.6 },
    { name: "Healthcare", size: 300.2 },
    { name: "Public Sector", size: 232.1 },
    { name: "Financial Services", size: 204.9 },
  ]

  const COLORS = [
    "var(--chart-1)",
    "var(--chart-2)",
    "var(--chart-3)",
    "var(--chart-4)",
    "var(--chart-5)",
  ]

  const CustomizedContent = (props: TreemapNode) => {
    const { x, y, width, height, name, size, depth } = props
    const value = size || props.value
    const color = COLORS[depth % COLORS.length]

    return (
      <g>
        <rect
          x={x}
          y={y}
          width={width}
          height={height}
          style={{
            fill: color,
            stroke: "#fff",
            strokeWidth: 2,
          }}
        />
        {width > 60 && height > 32 && (
          <text x={x + 8} y={y + 20} fontSize={12} fill="#fff">
            {name}
          </text>
        )}
        {width > 60 && height > 32 && (
          <text x={x + 8} y={y + 38} fontSize={12} fill="#fff">
            ${value?.toLocaleString()}
          </text>
        )}
      </g>
    )
  }

  return (
    <Card className="non-breakable">
      <CardContent className="flex flex-col gap-4">
        <CardTitle className="text-center">Industry Breakdown</CardTitle>

        <Tabs defaultValue="sales">
          <TabsList className="w-full">
            <TabsTrigger value="sales">By Sales</TabsTrigger>
            <TabsTrigger value="ebitda">By EBITDA</TabsTrigger>
          </TabsList>
        </Tabs>

        <ResponsiveContainer width="99%" height={300}>
          <Treemap
            isAnimationActive={false}
            data={treemapData}
            dataKey="size"
            stroke="#fff"
            content={React.createElement(CustomizedContent)}
          />
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
