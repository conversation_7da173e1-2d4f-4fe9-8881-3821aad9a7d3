import useSWRImmutable from "swr/immutable"

import {
  CashPosition,
  IncomeStatementCategory,
  WorkingCapital,
} from "@/types/finance"
import { CustomResponse } from "@/types/response"
import { createFetcher } from "@/services/fetcher"

export const useIncomeStatementBreakdown = () => {
  const url =
    "/accounting/journal-entry-transactions/sap-income-statement/?start_date=2024-01-01&end_date=2024-12-31"

  const snapshot = useSWRImmutable<CustomResponse<IncomeStatementCategory[]>>(
    url,
    createFetcher
  )
  return { ...snapshot }
}

export const useCashPosition = () => {
  const url = "/accounting/journal-entry-transactions/cash-position/"

  const snapshot = useSWRImmutable<CustomResponse<CashPosition>>(
    url,
    createFetcher
  )
  return { ...snapshot }
}

export const useWorkingCapital = () => {
  const url = "/accounting/journal-entry-transactions/get-working-capital/"

  const snapshot = useSWRImmutable<CustomResponse<WorkingCapital>>(
    url,
    createFetcher
  )
  return { ...snapshot }
}

export const useRevenue = () => {
  const url =
    "/accounting/journal-entry-transactions/filter-by-account/?account=Revenues&start_date=2024-01-01&end_date=2024-12-31"

  const snapshot = useSWRImmutable<{
    data: {
      results: []
    }
  }>(url, createFetcher)

  return { ...snapshot, data: snapshot.data?.data?.results }
}
