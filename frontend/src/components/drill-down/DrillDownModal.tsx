import React, { use<PERSON><PERSON><PERSON>, use<PERSON>emo, useState } from "react"
import {
  BarChart3,
  Calendar,
  Database,
  GripVertical,
  Hash,
  LineChart,
  PieChart,
  RotateCcw,
  Tag,
  X,
} from "lucide-react"
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Cell,
  LabelList,
  Line,
  Pie,
  <PERSON><PERSON>hart as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarProvider,
} from "@/components/ui/sidebar"
import {
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Table as UITable,
} from "@/components/ui/table"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"

// TypeScript interfaces for transaction analytics
interface TransactionRecord {
  id: number
  created_at: string
  updated_at: string
  transaction_id: number
  line_id: number
  transaction_date: string
  memo: string
  transaction_currency: string
  transaction_debit_amount: number
  transaction_credit_amount: number
  reporting_currency: string
  reporting_debit_amount: number
  reporting_credit_amount: number
  account_code: string
  offset_account_code: string
  reference_1: string
  reference_2: string
  base_reference: string
  is_adjustment: boolean
  entity: number
  journal_entry: number
  chart_of_account: number
}

// Pivot Table interfaces
interface PivotField {
  id: string
  name: string
  type: "dimension" | "measure"
  dataType: "string" | "number" | "date" | "boolean"
  iconType: "tag" | "calendar" | "hash"
  aggregation?: "sum" | "count" | "avg" | "min" | "max"
}

interface PivotConfiguration {
  filters: PivotField[]
  rows: PivotField[]
  columns: PivotField[]
  values: PivotField[]
}

type ViewMode = "data" | "chart"
type ChartType = "bar" | "line" | "pie" | "column" | "waterfall"

interface DragState {
  isDragging: boolean
  draggedField: PivotField | null
  dragOverZone: string | null
}

// Available fields for pivot table
const availableFields: PivotField[] = [
  // Dimensions
  {
    id: "entity",
    name: "Entity",
    type: "dimension",
    dataType: "number",
    iconType: "tag",
  },
  {
    id: "memo",
    name: "Memo",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "transaction_date",
    name: "Transaction Date",
    type: "dimension",
    dataType: "date",
    iconType: "calendar",
  },
  {
    id: "transaction_currency",
    name: "Currency",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "account_code",
    name: "Account Code",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "reference_1",
    name: "Reference 1",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "is_adjustment",
    name: "Is Adjustment",
    type: "dimension",
    dataType: "boolean",
    iconType: "tag",
  },
  // Measures
  {
    id: "reporting_debit_amount",
    name: "Reporting Debit",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "sum",
  },
  {
    id: "reporting_credit_amount",
    name: "Reporting Credit",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "sum",
  },
  {
    id: "transaction_count",
    name: "Transaction Count",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "count",
  },
  {
    id: "reporting_debit_amount_avg",
    name: "Avg Reporting Debit",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "avg",
  },
  {
    id: "reporting_credit_amount_max",
    name: "Max Reporting Credit",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "max",
  },
  {
    id: "reporting_debit_amount_min",
    name: "Min Reporting Debit",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "min",
  },
]

// Helper function to render icons
const renderFieldIcon = (iconType: "tag" | "calendar" | "hash") => {
  switch (iconType) {
    case "tag":
      return <Tag className="h-3 w-3" />
    case "calendar":
      return <Calendar className="h-3 w-3" />
    case "hash":
      return <Hash className="h-3 w-3" />
    default:
      return <Tag className="h-3 w-3" />
  }
}

const DrillDownModal = ({
  data,
  title,
  open,
  setOpen,
}: {
  data: TransactionRecord[]
  title: string
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
}) => {
  // Pivot table state management
  const [pivotConfig, setPivotConfig] = useState<PivotConfiguration>({
    filters: [],
    rows: [],
    columns: [],
    values: [],
  })
  const [viewMode, setViewMode] = useState<ViewMode>("data")
  const [chartType, setChartType] = useState<ChartType>("bar")
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedField: null,
    dragOverZone: null,
  })

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, field: PivotField) => {
    setDragState({
      isDragging: true,
      draggedField: field,
      dragOverZone: null,
    })
    e.dataTransfer.setData("application/json", JSON.stringify(field))
  }

  // Handle drag over
  const handleDragOver = (e: React.DragEvent, zone: string) => {
    e.preventDefault()
    setDragState((prev) => ({ ...prev, dragOverZone: zone }))
  }

  // Handle drop
  const handleDrop = (e: React.DragEvent, zone: keyof PivotConfiguration) => {
    e.preventDefault()
    const fieldData = e.dataTransfer.getData("application/json")
    if (fieldData) {
      const field: PivotField = JSON.parse(fieldData)

      // Remove field from all zones first
      const newConfig = {
        filters: pivotConfig.filters.filter((f) => f.id !== field.id),
        rows: pivotConfig.rows.filter((f) => f.id !== field.id),
        columns: pivotConfig.columns.filter((f) => f.id !== field.id),
        values: pivotConfig.values.filter((f) => f.id !== field.id),
      }

      // Add to target zone
      newConfig[zone] = [...newConfig[zone], field]

      setPivotConfig(newConfig)
    }

    setDragState({
      isDragging: false,
      draggedField: null,
      dragOverZone: null,
    })
  }

  // Remove field from zone
  const removeField = (fieldId: string, zone: keyof PivotConfiguration) => {
    setPivotConfig((prev) => ({
      ...prev,
      [zone]: prev[zone].filter((f) => f.id !== fieldId),
    }))
  }

  // Clear all fields
  const clearAll = () => {
    setPivotConfig({
      filters: [],
      rows: [],
      columns: [],
      values: [],
    })
  }

  // Auto-select chart type based on configuration
  const getRecommendedChartType = useCallback((): ChartType => {
    const hasRows = pivotConfig.rows.length > 0
    const hasColumns = pivotConfig.columns.length > 0
    const hasDateDimension = [...pivotConfig.rows, ...pivotConfig.columns].some(
      (field) => field.dataType === "date"
    )
    const hasFinancialMeasures = pivotConfig.values.some(
      (field) =>
        field.name.toLowerCase().includes("debit") ||
        field.name.toLowerCase().includes("credit") ||
        field.name.toLowerCase().includes("amount")
    )

    if (hasFinancialMeasures && hasRows && !hasColumns) return "waterfall"
    if (hasDateDimension) return "line"
    if (hasRows && !hasColumns && pivotConfig.rows.length === 1) return "pie"
    if (hasColumns && !hasRows) return "column"
    return "bar"
  }, [pivotConfig])

  // Update chart type when configuration changes
  React.useEffect(() => {
    setChartType(getRecommendedChartType())
  }, [pivotConfig, getRecommendedChartType])

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-h-[90vh] overflow-hidden p-0 sm:max-w-7xl">
        <SidebarProvider className="items-start">
          <Sidebar collapsible="none" className="hidden w-80 border-r md:flex">
            <SidebarContent className="p-4">
              {/* Available Fields */}
              <SidebarGroup>
                <SidebarGroupLabel className="px-0 text-sm font-semibold">
                  Available Fields
                </SidebarGroupLabel>
                <SidebarGroupContent>
                  <div className="space-y-2">
                    <div className="text-muted-foreground mb-2 text-xs">
                      Dimensions
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {availableFields
                        .filter((field) => field.type === "dimension")
                        .map((field) => (
                          <div
                            key={field.id}
                            draggable
                            onDragStart={(e) => handleDragStart(e, field)}
                            className="flex cursor-move items-center gap-1 rounded bg-blue-100 px-2 py-1 text-xs text-blue-800 transition-colors hover:bg-blue-200"
                          >
                            {renderFieldIcon(field.iconType)}
                            {field.name}
                            <GripVertical className="h-3 w-3" />
                          </div>
                        ))}
                    </div>

                    <div className="text-muted-foreground mt-4 mb-2 text-xs">
                      Measures
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {availableFields
                        .filter((field) => field.type === "measure")
                        .map((field) => (
                          <div
                            key={field.id}
                            draggable
                            onDragStart={(e) => handleDragStart(e, field)}
                            className="flex cursor-move items-center gap-1 rounded bg-green-100 px-2 py-1 text-xs text-green-800 transition-colors hover:bg-green-200"
                          >
                            {renderFieldIcon(field.iconType)}
                            {field.name}
                            <GripVertical className="h-3 w-3" />
                          </div>
                        ))}
                    </div>
                  </div>
                </SidebarGroupContent>
              </SidebarGroup>

              {/* Pivot Configuration */}
              <SidebarGroup className="mt-6">
                <SidebarGroupLabel className="px-0 text-sm font-semibold">
                  Pivot Configuration
                </SidebarGroupLabel>
                <SidebarGroupContent className="space-y-4">
                  {/* Filters Drop Zone */}
                  <div>
                    <div className="text-muted-foreground mb-1 text-xs">
                      Filters
                    </div>
                    <div
                      onDragOver={(e) => handleDragOver(e, "filters")}
                      onDrop={(e) => handleDrop(e, "filters")}
                      className={cn(
                        "min-h-[40px] rounded border-2 border-dashed p-2 transition-colors",
                        dragState.dragOverZone === "filters"
                          ? "border-blue-400 bg-blue-50"
                          : "border-gray-300",
                        pivotConfig.filters.length === 0 &&
                          "text-muted-foreground flex items-center justify-center text-xs"
                      )}
                    >
                      {pivotConfig.filters.length === 0 ? (
                        "Drop filters here"
                      ) : (
                        <div className="flex flex-wrap gap-1">
                          {pivotConfig.filters.map((field) => (
                            <div
                              key={field.id}
                              className="flex items-center gap-1 rounded bg-orange-100 px-2 py-1 text-xs text-orange-800"
                            >
                              {renderFieldIcon(field.iconType)}
                              {field.name}
                              <X
                                className="h-3 w-3 cursor-pointer hover:text-orange-600"
                                onClick={() => removeField(field.id, "filters")}
                              />
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Rows Drop Zone */}
                  <div>
                    <div className="text-muted-foreground mb-1 text-xs">
                      Rows
                    </div>
                    <div
                      onDragOver={(e) => handleDragOver(e, "rows")}
                      onDrop={(e) => handleDrop(e, "rows")}
                      className={cn(
                        "min-h-[40px] rounded border-2 border-dashed p-2 transition-colors",
                        dragState.dragOverZone === "rows"
                          ? "border-blue-400 bg-blue-50"
                          : "border-gray-300",
                        pivotConfig.rows.length === 0 &&
                          "text-muted-foreground flex items-center justify-center text-xs"
                      )}
                    >
                      {pivotConfig.rows.length === 0 ? (
                        "Drop row fields here"
                      ) : (
                        <div className="flex flex-wrap gap-1">
                          {pivotConfig.rows.map((field) => (
                            <div
                              key={field.id}
                              className="flex items-center gap-1 rounded bg-purple-100 px-2 py-1 text-xs text-purple-800"
                            >
                              {renderFieldIcon(field.iconType)}
                              {field.name}
                              <X
                                className="h-3 w-3 cursor-pointer hover:text-purple-600"
                                onClick={() => removeField(field.id, "rows")}
                              />
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Columns Drop Zone */}
                  <div>
                    <div className="text-muted-foreground mb-1 text-xs">
                      Columns
                    </div>
                    <div
                      onDragOver={(e) => handleDragOver(e, "columns")}
                      onDrop={(e) => handleDrop(e, "columns")}
                      className={cn(
                        "min-h-[40px] rounded border-2 border-dashed p-2 transition-colors",
                        dragState.dragOverZone === "columns"
                          ? "border-blue-400 bg-blue-50"
                          : "border-gray-300",
                        pivotConfig.columns.length === 0 &&
                          "text-muted-foreground flex items-center justify-center text-xs"
                      )}
                    >
                      {pivotConfig.columns.length === 0 ? (
                        "Drop column fields here"
                      ) : (
                        <div className="flex flex-wrap gap-1">
                          {pivotConfig.columns.map((field) => (
                            <div
                              key={field.id}
                              className="flex items-center gap-1 rounded bg-indigo-100 px-2 py-1 text-xs text-indigo-800"
                            >
                              {renderFieldIcon(field.iconType)}
                              {field.name}
                              <X
                                className="h-3 w-3 cursor-pointer hover:text-indigo-600"
                                onClick={() => removeField(field.id, "columns")}
                              />
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Values Drop Zone */}
                  <div>
                    <div className="text-muted-foreground mb-1 text-xs">
                      Values
                    </div>
                    <div
                      onDragOver={(e) => handleDragOver(e, "values")}
                      onDrop={(e) => handleDrop(e, "values")}
                      className={cn(
                        "min-h-[40px] rounded border-2 border-dashed p-2 transition-colors",
                        dragState.dragOverZone === "values"
                          ? "border-blue-400 bg-blue-50"
                          : "border-gray-300",
                        pivotConfig.values.length === 0 &&
                          "text-muted-foreground flex items-center justify-center text-xs"
                      )}
                    >
                      {pivotConfig.values.length === 0 ? (
                        "Drop measure fields here"
                      ) : (
                        <div className="flex flex-wrap gap-1">
                          {pivotConfig.values.map((field) => (
                            <div
                              key={field.id}
                              className="flex items-center gap-1 rounded bg-emerald-100 px-2 py-1 text-xs text-emerald-800"
                            >
                              {renderFieldIcon(field.iconType)}
                              {field.name}
                              <X
                                className="h-3 w-3 cursor-pointer hover:text-emerald-600"
                                onClick={() => removeField(field.id, "values")}
                              />
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Clear All Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearAll}
                    className="mt-4 w-full"
                  >
                    <RotateCcw className="mr-1 h-3 w-3" />
                    Clear All
                  </Button>
                </SidebarGroupContent>
              </SidebarGroup>
            </SidebarContent>
          </Sidebar>

          <div className="flex h-[90vh] flex-1 flex-col overflow-hidden">
            <DialogHeader className="border-b p-4">
              <DialogTitle>{title} - Pivot Analytics</DialogTitle>
              <DialogDescription>
                Drag and drop fields to build dynamic pivot tables and charts
              </DialogDescription>

              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Tabs
                    value={viewMode}
                    onValueChange={(value) => setViewMode(value as ViewMode)}
                  >
                    <TabsList className="h-8">
                      <TabsTrigger value="data" className="text-xs">
                        <Database className="mr-1 h-3 w-3" />
                        Data
                      </TabsTrigger>
                      <TabsTrigger value="chart" className="text-xs">
                        <BarChart3 className="mr-1 h-3 w-3" />
                        Chart
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>

                  {viewMode === "chart" && (
                    <Select
                      value={chartType}
                      onValueChange={(value) =>
                        setChartType(value as ChartType)
                      }
                    >
                      <SelectTrigger className="h-8 w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="bar">
                          <div className="flex items-center gap-2">
                            <BarChart3 className="h-3 w-3" />
                            Bar
                          </div>
                        </SelectItem>
                        <SelectItem value="line">
                          <div className="flex items-center gap-2">
                            <LineChart className="h-3 w-3" />
                            Line
                          </div>
                        </SelectItem>
                        <SelectItem value="pie">
                          <div className="flex items-center gap-2">
                            <PieChart className="h-3 w-3" />
                            Pie
                          </div>
                        </SelectItem>
                        <SelectItem value="column">
                          <div className="flex items-center gap-2">
                            <BarChart3 className="h-3 w-3" />
                            Column
                          </div>
                        </SelectItem>
                        <SelectItem value="waterfall">
                          <div className="flex items-center gap-2">
                            <BarChart3 className="h-3 w-3" />
                            Waterfall
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                </div>

                <div className="text-muted-foreground text-xs">
                  {pivotConfig.rows.length +
                    pivotConfig.columns.length +
                    pivotConfig.values.length >
                  0
                    ? `${pivotConfig.rows.length} rows, ${pivotConfig.columns.length} columns, ${pivotConfig.values.length} values`
                    : "Drag fields from the sidebar to get started"}
                </div>
              </div>
            </DialogHeader>

            <div className="flex flex-1 flex-col gap-4 overflow-y-auto p-4">
              {/* Main Content Area */}
              {pivotConfig.rows.length === 0 &&
              pivotConfig.columns.length === 0 &&
              pivotConfig.values.length === 0 ? (
                // Empty state
                <div className="flex flex-1 items-center justify-center">
                  <div className="text-center">
                    <Database className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                    <h3 className="mb-2 text-lg font-semibold">
                      Build Your Analysis
                    </h3>
                    <p className="text-muted-foreground mb-4 max-w-md">
                      Drag fields from the sidebar to create dynamic pivot
                      tables and charts. Start by adding dimensions to rows or
                      columns, then add measures to values.
                    </p>
                    <div className="text-muted-foreground text-sm">
                      <div className="mb-2">
                        💡 <strong>Quick Start:</strong>
                      </div>
                      <div>• Drag {`"Entity"`} to Rows</div>
                      <div>• Drag {`"Reporting Credit"`} to Values</div>
                      <div>• Toggle to Chart view</div>
                    </div>
                  </div>
                </div>
              ) : viewMode === "data" ? (
                // Data View - Pivot Table
                <PivotTableView config={pivotConfig} data={data} />
              ) : (
                // Chart View
                <ChartView
                  config={pivotConfig}
                  data={data}
                  chartType={chartType}
                />
              )}
            </div>
          </div>
        </SidebarProvider>
      </DialogContent>
    </Dialog>
  )
}

// Helper function to apply filters to data
const applyFilters = (
  data: TransactionRecord[],
  filters: PivotField[]
): TransactionRecord[] => {
  if (filters.length === 0) return data

  // For now, we'll just return all data since we don't have filter values
  // In a real implementation, you'd apply actual filter conditions here
  return data
}

// Helper function to calculate aggregated values
const calculateAggregation = (
  values: number[],
  aggregation: "sum" | "count" | "avg" | "min" | "max"
): number => {
  if (values.length === 0) return 0

  switch (aggregation) {
    case "sum":
      return values.reduce((sum, val) => sum + val, 0)
    case "count":
      return values.length
    case "avg":
      return values.reduce((sum, val) => sum + val, 0) / values.length
    case "min":
      return Math.min(...values)
    case "max":
      return Math.max(...values)
    default:
      return 0
  }
}

// Helper function to get the actual data field name from a field ID
const getDataFieldName = (fieldId: string): string => {
  // Handle aggregated field IDs that reference the same data field
  if (fieldId.startsWith("reporting_debit_amount")) {
    return "reporting_debit_amount"
  }
  if (fieldId.startsWith("reporting_credit_amount")) {
    return "reporting_credit_amount"
  }
  return fieldId
}

// Helper function to generate pivot table data
const generatePivotData = (
  data: TransactionRecord[],
  config: PivotConfiguration
): Record<string, unknown>[] => {
  if (config.values.length === 0 || config.rows.length === 0) return []

  // Apply filters first
  const filteredData = applyFilters(data, config.filters)

  // Group data by row dimensions
  const grouped = filteredData.reduce(
    (acc, record) => {
      const recordData = record as unknown as Record<string, unknown>

      // Create composite key from all row fields
      const rowKey = config.rows
        .map((field) => String(recordData[field.id] || ""))
        .join("|")

      if (!acc[rowKey]) {
        acc[rowKey] = {
          // Add row dimension values
          ...config.rows.reduce(
            (rowAcc, field) => {
              rowAcc[field.name] = recordData[field.id]
              return rowAcc
            },
            {} as Record<string, unknown>
          ),
          // Initialize value arrays for aggregation
          _rawValues: config.values.reduce(
            (valueAcc, valueField) => {
              valueAcc[valueField.id] = []
              return valueAcc
            },
            {} as Record<string, number[]>
          ),
          _count: 0,
        }
      }

      // Collect raw values for aggregation
      const groupData = acc[rowKey] as {
        _count: number
        _rawValues: Record<string, number[]>
        [key: string]: unknown
      }

      groupData._count += 1
      config.values.forEach((valueField) => {
        // Get the actual data field name for this value field
        const dataFieldName = getDataFieldName(valueField.id)
        const value = Number(recordData[dataFieldName]) || 0
        groupData._rawValues[valueField.id].push(value)
      })

      return acc
    },
    {} as Record<string, Record<string, unknown>>
  )

  // Calculate final aggregated values
  return Object.values(grouped).map((group) => {
    const result = { ...group }
    const groupData = group as {
      _count: number
      _rawValues: Record<string, number[]>
      [key: string]: unknown
    }

    // Calculate aggregated values
    config.values.forEach((valueField) => {
      const rawValues = groupData._rawValues[valueField.id]
      if (valueField.aggregation === "count") {
        result[valueField.name] = groupData._count
      } else {
        result[valueField.name] = calculateAggregation(
          rawValues,
          valueField.aggregation || "sum"
        )
      }
    })

    // Remove internal fields
    delete result._rawValues
    delete result._count

    return result
  })
}

// Pivot Table View Component
const PivotTableView = ({
  config,
  data,
}: {
  config: PivotConfiguration
  data: TransactionRecord[]
}) => {
  // Generate pivot table data based on configuration
  const pivotData = useMemo(() => {
    return generatePivotData(data, config)
  }, [config, data])

  if (config.values.length === 0) {
    return (
      <div className="text-muted-foreground flex h-64 items-center justify-center">
        Add measures to the Values area to see data
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Pivot Table</CardTitle>
      </CardHeader>
      <CardContent>
        <UITable>
          <TableHeader>
            <TableRow>
              {config.rows.map((field) => (
                <TableHead key={field.id}>{field.name}</TableHead>
              ))}
              {config.values.map((field) => (
                <TableHead key={field.id} className="text-right">
                  {field.name}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {pivotData.map((row, index) => (
              <TableRow key={index}>
                {config.rows.map((field) => (
                  <TableCell key={field.id}>
                    {String(row[field.name] || "")}
                  </TableCell>
                ))}
                {config.values.map((field) => (
                  <TableCell key={field.id} className="text-right font-mono">
                    {field.dataType === "number"
                      ? `$${Number(row[field.name] || 0).toFixed(2)}`
                      : String(row[field.name] || "")}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </UITable>
      </CardContent>
    </Card>
  )
}

// Helper function to generate chart data
const generateChartData = (
  data: TransactionRecord[],
  config: PivotConfiguration
): Array<{ name: string; value: number }> => {
  if (config.values.length === 0 || config.rows.length === 0) return []

  // Use the same pivot data generation logic but format for charts
  const pivotData = generatePivotData(data, config)
  const rowField = config.rows[0]
  const valueField = config.values[0]

  return pivotData.map((row) => ({
    name: String(row[rowField.name] || ""),
    value: Number(row[valueField.name] || 0),
  }))
}

// Helper function to generate waterfall chart data
const generateWaterfallData = (
  data: TransactionRecord[],
  config: PivotConfiguration
): Array<{ name: string; amount: number; remaining: number }> => {
  if (config.values.length === 0 || config.rows.length === 0) return []

  // Get the basic chart data first
  const chartData = generateChartData(data, config)

  if (chartData.length === 0) return []

  // Sort data by absolute value to create a meaningful waterfall
  const sortedData = [...chartData].sort(
    (a, b) => Math.abs(b.value) - Math.abs(a.value)
  )

  // Calculate waterfall structure for year-over-year analysis
  // Simulate previous year data (for demo purposes, use 80% of current values)
  const previousYearTotal =
    sortedData.reduce((sum, item) => sum + item.value, 0) * 0.8

  let runningTotal = previousYearTotal

  const waterfallData: Array<{
    name: string
    amount: number
    remaining: number
  }> = []

  // Add starting point (previous year)
  waterfallData.push({
    name: "Previous\nYear",
    amount: previousYearTotal,
    remaining: 0,
  })

  // Add changes for each category
  sortedData.forEach((item) => {
    // Calculate change vs previous year (simulate 20% growth with some variation)
    const previousValue = item.value * 0.8
    const change = item.value - previousValue
    const remaining = runningTotal
    runningTotal += change

    waterfallData.push({
      name: item.name.replace(/\s+/g, "\n"), // Add line breaks for better display
      amount: change,
      remaining: remaining,
    })
  })

  // Add final total (current year)
  waterfallData.push({
    name: "Current\nYear",
    amount: runningTotal,
    remaining: 0,
  })

  return waterfallData
}

// Helper function to format currency for display
const formatCurrency = (value: number, decimals: number = 0): string => {
  return Math.abs(value).toLocaleString(undefined, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  })
}

// Custom tick component for waterfall charts
const WrappedTick = (props: {
  x?: number
  y?: number
  payload?: { value: string }
}) => {
  const { x, y, payload } = props
  const lines = payload?.value?.split("\n") || []

  return (
    <g transform={`translate(${x || 0},${y || 0})`}>
      {lines.map((line: string, index: number) => (
        <text
          key={index}
          x={0}
          y={index * 12}
          dy={16}
          textAnchor="middle"
          fill="#666"
          fontSize="12"
        >
          {line}
        </text>
      ))}
    </g>
  )
}

// Chart View Component
const ChartView = ({
  config,
  data,
  chartType,
}: {
  config: PivotConfiguration
  data: TransactionRecord[]
  chartType: ChartType
}) => {
  // Generate chart data based on configuration
  const chartData = useMemo(() => {
    return generateChartData(data, config)
  }, [config, data])

  // Generate waterfall chart data
  const waterfallData = useMemo(() => {
    return generateWaterfallData(data, config)
  }, [config, data])

  if (config.values.length === 0 || config.rows.length === 0) {
    return (
      <div className="text-muted-foreground flex h-64 items-center justify-center">
        Add dimensions to Rows and measures to Values to see charts
      </div>
    )
  }

  const COLORS = ["#3b82f6", "#ef4444", "#10b981", "#f59e0b", "#8b5cf6"]

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {config.values[0]?.name} by {config.rows[0]?.name}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            {chartType === "bar" ? (
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#3b82f6" />
              </BarChart>
            ) : chartType === "line" ? (
              <RechartsLineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#3b82f6"
                  strokeWidth={2}
                />
              </RechartsLineChart>
            ) : chartType === "pie" ? (
              <RechartsPieChart>
                <Pie
                  data={chartData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={120}
                  label={({ name, percent }) =>
                    `${name} (${(percent * 100).toFixed(0)}%)`
                  }
                >
                  {chartData.map((_, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip />
              </RechartsPieChart>
            ) : chartType === "waterfall" ? (
              <BarChart data={waterfallData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" tick={WrappedTick} interval={0} />
                <YAxis />
                <Tooltip
                  formatter={(value: number, name: string) => [
                    `$${formatCurrency(Math.abs(value))}`,
                    name === "remaining" ? "Base" : "Amount",
                  ]}
                />
                <Bar dataKey="remaining" stackId="a" fill="transparent" />
                <Bar
                  dataKey="amount"
                  name="Amount"
                  stackId="a"
                  fill="var(--chart-1)"
                >
                  {waterfallData.map((item, index) => {
                    if (item.amount < 0) {
                      return <Cell key={index} fill="var(--chart-3)" />
                    }
                    if (index === waterfallData.length - 1) {
                      return <Cell key={index} fill="var(--chart-5)" />
                    }
                    return <Cell key={index} fill="var(--chart-1)" />
                  })}
                  <LabelList
                    dataKey="amount"
                    position="top"
                    formatter={(value: number) =>
                      `$${formatCurrency(Math.abs(value))}`
                    }
                    fill="var(--foreground)"
                    fontSize={12}
                  />
                </Bar>
              </BarChart>
            ) : (
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#3b82f6" />
              </BarChart>
            )}
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}

export default DrillDownModal
